<template>
  <div ref="chartRef" class="echarts-container"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// 定义props
const props = defineProps({
  chartOption: {
    type: Object,
    required: true,
    default: () => ({})
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '400px'
  }
})

// 定义emits
const emit = defineEmits(['chart-click', 'chart-ready'])

// 图表容器引用
const chartRef = ref(null)
let chartInstance = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 创建新的图表实例
  chartInstance = echarts.init(chartRef.value)
  
  // 设置图表配置
  if (props.chartOption && Object.keys(props.chartOption).length > 0) {
    chartInstance.setOption(props.chartOption, true)
  }
  
  // 绑定点击事件
  chartInstance.on('click', (params) => {
    emit('chart-click', params)
  })
  
  // 触发图表就绪事件
  emit('chart-ready', chartInstance)
}

// 监听配置变化
watch(
  () => props.chartOption,
  (newOption) => {
    if (chartInstance && newOption && Object.keys(newOption).length > 0) {
      chartInstance.setOption(newOption, true)
    }
  },
  { deep: true }
)

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法给父组件
defineExpose({
  getChartInstance: () => chartInstance,
  resize: handleResize
})
</script>

<style scoped>
.echarts-container {
  width: v-bind(width);
  height: v-bind(height);
  min-height: 300px;
}
</style>
