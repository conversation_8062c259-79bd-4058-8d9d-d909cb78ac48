<template>
  <div class="map-container">
    <echartsInit
      :chartOption="mapOption"
      @chart-click="handleMapClick"
      ref="mapRef"
      width="100%"
      height="100vh"
    />
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import echartsInit from './echartsInit.vue'
import chinaJson from '@/utils/map/中华人民共和国.json'

// 地图引用
const mapRef = ref(null)

// 地图配置
const mapOption = reactive({})
// 基地数据
const baseData = [
  {
    name: '华东基地',
    value: [121.4737, 31.2304, 3500],
    capacity: '3500吨/月',
    status: 'running',
    employees: 280,
    description: '主要生产高精度锂电铜箔，配备最新的电解设备',
    id: 'huadong',
  },
  {
    name: '华南基地',
    value: [113.2644, 23.1291, 2800],
    capacity: '2800吨/月',
    status: 'running',
    employees: 220,
    description: '专注新能源汽车用铜箔，技术领先',
    id: 'huanan',
  },
  {
    name: '西南基地',
    value: [104.0665, 30.5728, 2200],
    capacity: '2200吨/月',
    status: 'running',
    employees: 180,
    description: '储能系统铜箔生产基地，技术先进',
    id: 'xinan',
  },
]
// 地图点击处理
const handleMapClick = (params) => {
  console.log('地图点击事件:', params)
}

// 初始化地图配置
const initMapOption = () => {
  // 注册中国地图，chinaJson 是 GeoJSON 格式的地图数据
  echarts.registerMap('china', chinaJson)

  // 将地图配置赋值到 mapOption 对象中
  Object.assign(mapOption, {
    // 图表背景设为透明
    backgroundColor: 'transparent',
    // 地理坐标配置项（地图主体）
    geo: {
      // 使用前面注册的“中国”地图
      map: 'china',
      // 允许地图拖动和平移
      roam: true,
      // 设置初始缩放级别
      zoom: 1.2,
      // 设置地图初始中心点位置（经度、纬度）
      center: [104, 35],
      // 缩放限制范围
      scaleLimit: {
        min: 0.8, // 最小缩放倍数
        max: 3,   // 最大缩放倍数
      },
      // 区域名称标签配置
      label: {
        show: false, // 不显示省市名称
        color: 'rgba(0, 212, 255, 0.6)', // 标签颜色
        fontSize: 12, // 字体大小
      },
      // 区域样式配置
      itemStyle: {
        areaColor: 'rgba(0, 59, 92, 0.2)', // 区域填充色（带透明）
        borderColor: 'rgba(0, 212, 255, 0.3)', // 边框颜色
        borderWidth: 1, // 边框宽度
      },
      // 鼠标悬浮时的高亮样式
      emphasis: {
        label: {
          show: false, // 悬浮时也不显示省市名称
        },
        itemStyle: {
          areaColor: 'rgba(0, 91, 139, 0.4)', // 悬浮背景色
          borderColor: '#00d4ff', // 悬浮边框色
          borderWidth: 2, // 悬浮边框宽
        },
      },
    },

    // 图表数据系列（只有一个 scatter）
    series: [
      {
        name: '基地分布', // 系列名称
        type: 'scatter', // 类型为散点图
        coordinateSystem: 'geo', // 使用地理坐标系统
        data: baseData, // 绑定基地数据

        // 使用图钉图标
        symbol: 'pin',

        // 根据产能动态调整图钉大小（避免大小极端）
        symbolSize: (val) => {
          const capacity = val[2] // val[2] 表示工厂产能
          return Math.max(35, Math.min(60, capacity / 80 + 25))
        },

        // 图钉颜色配置
        itemStyle: {
          color: '#00d4ff', // 图钉颜色
          shadowBlur: 10,
          shadowColor: 'rgba(0, 212, 255, 0.5)',
        },

        // 标签配置
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          fontSize: 12,
          formatter: '{b}', // 显示基地名称
        },

        // 鼠标悬浮高亮效果
        emphasis: {
          scale: 1.3, // 点放大比例
          label: {
            show: true, // 显示标签
            color: '#ffffff', // 标签文字颜色
            fontSize: 14, // 字体大小
            formatter: (params) => {
              const data = params.data
              return `${data.name}\n${data.capacity}\n员工: ${data.employees}人`
            }
          },
          itemStyle: {
            color: '#ffff00', // 悬浮时变为黄色
            shadowBlur: 20,
            shadowColor: 'rgba(255, 255, 0, 0.8)',
          }
        },
      },
    ],
  })
}

// 在组件挂载后执行地图初始化函数
onMounted(() => {
  initMapOption()
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  overflow: hidden;
}
</style>