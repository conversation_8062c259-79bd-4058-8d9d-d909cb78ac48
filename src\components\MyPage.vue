<template>
  <div class="base-overview-card data-card">
    <div class="data-header">
      <decoFrameB2 :config="decoFrameConfig">
        <i class="i carbon:earth-southeast-asia icon"></i>
      </decoFrameB2>
      <span class="title">全国基地总览</span>
    </div>
    <div class="data-card-body">
      <div class="screenA-counterGrid">
        <aYinTechBorderB3
          :config="borderConfig(index)"
          v-for="(item, index) in globalOverviewData.arry"
          :key="index"
        >
          <div class="inner-content">
            <div class="block-title">
              {{ item.title }} <span v-if="item.unit">({{ item.unit }})</span>
            </div>
            <div class="total">
              <i :class="[item.icon, 'icon']"></i>
              <DigitalTransform
                class="numbers"
                :value="item.total"
                :useGrouping="true"
                :interval="1000"
              />
            </div>
          </div>
        </aYinTechBorderB3>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { useDataService } from '@/composables/useDataService'

// 数据服务
const dataService = useDataService()

// 全国基地总览数据
const globalOverviewData = reactive(dataService.getGlobalOverviewData())

// 边框动画配置
const borderConfig = (index) => {
  let rotate = null
  if (index === 0) rotate = 'x'
  else if (index === 1) rotate = 'all'
  else if (index === 3) rotate = 'y'
  return { dur: 3, opacity: 0.7, rotate }
}

// 边框装饰配置
const decoFrameConfig = {
  directionAlt: true,
  scale: 0.8,
  glow: true,
  glowColor: '#00d4ff',
  decorationColor: ['#00d4ff', '#0099cc'],
  textColor: '#00d4ff',
  height: 40,
}
</script>

<style scoped lang="less">
.data-header {
  display: flex;
  align-items: center;
  height: 42px;
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.15),
    rgba(0, 212, 255, 0.05)
  );
  backdrop-filter: blur(10px);

  .icon {
    font-size: 45px;
    color: #00d4ff;
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.5));
  }

  .title {
    color: #75d1f0;
    font-size: 26px;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
}

.data-card-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: auto;
  padding: 8px;
  position: relative;
}

.screenA-counterGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  height: 145px;
  grid-template-rows: repeat(2, 1fr);
  position: relative;

  .aYinTechBorderB3 {
    padding: 0;
    position: relative;

    .inner-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 8px;

      .block-title {
        font-size: 12px;
        color: #00d4ff;
        margin-bottom: 6px;
        text-align: center;
        line-height: 1.2;
        font-weight: 600;
      }

      .total {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        i {
          font-size: 22px;
          color: #00d4ff;
          flex-shrink: 0;
          filter: drop-shadow(0 2px 4px rgba(0, 212, 255, 0.4));
        }

        .numbers {
          font-size: 22px;
          color: #00d4ff;
          font-weight: 800;
          line-height: 1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
</style>
