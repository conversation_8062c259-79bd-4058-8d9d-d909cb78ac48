<template>
  <div class="my-page">
    <header class="page-header">
      <h1>我的新页面</h1>
      <p>这是一个全新的页面组件</p>
    </header>
    
    <main class="page-content">
      <section class="welcome-section">
        <h2>欢迎使用</h2>
        <p>您可以在这里添加任何内容</p>
      </section>
      
      <section class="features-section">
        <h3>功能特性</h3>
        <ul>
          <li>响应式设计</li>
          <li>Vue 3 Composition API</li>
          <li>现代化样式</li>
        </ul>
      </section>
      
      <section class="interactive-section">
        <h3>交互示例</h3>
        <button @click="handleClick" class="action-btn">
          点击次数: {{ clickCount }}
        </button>
        <input 
          v-model="inputText" 
          placeholder="输入一些文字..."
          class="text-input"
        />
        <p v-if="inputText">您输入的内容: {{ inputText }}</p>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const clickCount = ref(0)
const inputText = ref('')

// 方法
const handleClick = () => {
  clickCount.value++
}
</script>

<style scoped>
.my-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
}

.page-header p {
  margin: 0;
  opacity: 0.9;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-section,
.features-section,
.interactive-section {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.welcome-section h2,
.features-section h3,
.interactive-section h3 {
  color: #333;
  margin-top: 0;
}

.features-section ul {
  list-style-type: none;
  padding: 0;
}

.features-section li {
  padding: 8px 0;
  border-bottom: 1px solid #ddd;
}

.features-section li:last-child {
  border-bottom: none;
}

.interactive-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-btn {
  padding: 10px 20px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s;
}

.action-btn:hover {
  background: #45a049;
}

.text-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.text-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}

@media (max-width: 600px) {
  .my-page {
    padding: 10px;
  }
  
  .page-header h1 {
    font-size: 2em;
  }
}
</style>
