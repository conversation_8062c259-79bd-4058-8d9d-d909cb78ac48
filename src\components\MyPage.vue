<template>
        <echartsInit
        :chartOption="mapOption"
        @chart-click="handleMapClick"
        ref="mapRef"
      />
</template>
<script> 
import { reactive, ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import chinaJson from '@/utils/map/中华人民共和国.json'
// 地图引用
const mapRef = ref(null)
// 地图配置
const mapOption = reactive({})
// 初始化地图配置
const initMapOption = () => {
  // 注册中国地图，chinaJson 是 GeoJSON 格式的地图数据
  echarts.registerMap('china', chinaJson)

  // 将地图配置赋值到 mapOption 对象中
  Object.assign(mapOption, {
    // 图表背景设为透明
    backgroundColor: 'transparent',
    // 地理坐标配置项（地图主体）
    geo: {
      // 使用前面注册的“中国”地图
      map: 'china',
      // 允许地图拖动和平移
      roam: true,
      // 设置初始缩放级别
      zoom: 1.2,
      // 设置地图初始中心点位置（经度、纬度）
      center: [104, 35],
      // 缩放限制范围
      scaleLimit: {
        min: 0.8, // 最小缩放倍数
        max: 3,   // 最大缩放倍数
      },
      // 区域名称标签配置
      label: {
        show: false, // 不显示省市名称
        color: 'rgba(0, 212, 255, 0.6)', // 标签颜色
        fontSize: 12, // 字体大小
      },
      // 区域样式配置
      itemStyle: {
        areaColor: 'rgba(0, 59, 92, 0.2)', // 区域填充色（带透明）
        borderColor: 'rgba(0, 212, 255, 0.3)', // 边框颜色
        borderWidth: 1, // 边框宽度
      },
      // 鼠标悬浮时的高亮样式
      emphasis: {
        label: {
          show: false, // 悬浮时也不显示省市名称
        },
        itemStyle: {
          areaColor: 'rgba(0, 91, 139, 0.4)', // 悬浮背景色
          borderColor: '#00d4ff', // 悬浮边框色
          borderWidth: 2, // 悬浮边框宽
        },
      },
    },

    // 图表数据系列（只有一个 scatter）
    series: [
      {
        name: '基地分布', // 系列名称
        type: 'scatter', // 类型为散点图
        coordinateSystem: 'geo', // 使用地理坐标系统

        // 使用图钉图标
        symbol: 'pin',

        // 根据产能动态调整图钉大小（避免大小极端）
        symbolSize: (val) => {
          const capacity = val[2] // val[2] 表示工厂产能
          return Math.max(35, Math.min(60, capacity / 80 + 25))
        },
        // 鼠标悬浮高亮效果
        emphasis: {
          scale: 1.3, // 点放大比例
          label: {
            show: true, // 显示标签
            color: '#ffffff', // 标签文字颜色
            fontSize: 14, // 字体大小
          },
        },
      },
    ],
  })
}

// 在组件挂载后执行地图初始化函数
onMounted(() => {
  initMapOption()
})

</script>
<style></style>