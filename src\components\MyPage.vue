<template>
  <div class="dashboard">
    <header class="dashboard-header">
      <h1>数据可视化仪表板</h1>
      <div class="resolution-info">
        <span>当前分辨率: {{ screenResolution.width }} × {{ screenResolution.height }}</span>
        <span class="device-type">{{ deviceType }}</span>
      </div>
    </header>

    <main class="dashboard-content">
      <!-- 图表网格容器 -->
      <div class="charts-grid">
        <!-- 柱状图 -->
        <div class="chart-container">
          <h3>销售数据统计</h3>
          <div ref="barChartRef" class="chart"></div>
        </div>

        <!-- 折线图 -->
        <div class="chart-container">
          <h3>趋势分析</h3>
          <div ref="lineChartRef" class="chart"></div>
        </div>

        <!-- 饼图 -->
        <div class="chart-container">
          <h3>市场份额</h3>
          <div ref="pieChartRef" class="chart"></div>
        </div>

        <!-- 仪表盘 -->
        <div class="chart-container">
          <h3>性能指标</h3>
          <div ref="gaugeChartRef" class="chart"></div>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="control-panel">
        <h3>图表控制</h3>
        <div class="controls">
          <button @click="refreshCharts" class="control-btn">刷新数据</button>
          <button @click="toggleTheme" class="control-btn">
            {{ isDarkTheme ? '浅色主题' : '深色主题' }}
          </button>
          <select v-model="selectedChart" @change="highlightChart" class="chart-selector">
            <option value="">选择图表</option>
            <option value="bar">柱状图</option>
            <option value="line">折线图</option>
            <option value="pie">饼图</option>
            <option value="gauge">仪表盘</option>
          </select>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// 图表引用
const barChartRef = ref(null)
const lineChartRef = ref(null)
const pieChartRef = ref(null)
const gaugeChartRef = ref(null)

// 图表实例
let barChart = null
let lineChart = null
let pieChart = null
let gaugeChart = null

// 响应式数据
const screenResolution = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

const isDarkTheme = ref(false)
const selectedChart = ref('')

// 计算设备类型
const deviceType = ref('')

// 更新设备类型
const updateDeviceType = () => {
  const width = screenResolution.value.width
  if (width < 768) {
    deviceType.value = '移动设备'
  } else if (width < 1024) {
    deviceType.value = '平板设备'
  } else {
    deviceType.value = '桌面设备'
  }
}

// 监听窗口大小变化
const handleResize = () => {
  screenResolution.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
  updateDeviceType()

  // 重新调整图表大小
  nextTick(() => {
    if (barChart) barChart.resize()
    if (lineChart) lineChart.resize()
    if (pieChart) pieChart.resize()
    if (gaugeChart) gaugeChart.resize()
  })
}

// 初始化柱状图
const initBarChart = () => {
  barChart = echarts.init(barChartRef.value, isDarkTheme.value ? 'dark' : 'light')
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['销量', '利润']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '销量',
        type: 'bar',
        data: [120, 200, 150, 80, 70, 110]
      },
      {
        name: '利润',
        type: 'bar',
        data: [60, 100, 75, 40, 35, 55]
      }
    ]
  }
  barChart.setOption(option)
}

// 初始化折线图
const initLineChart = () => {
  lineChart = echarts.init(lineChartRef.value, isDarkTheme.value ? 'dark' : 'light')
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['访问量', '转化率']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '访问量'
      },
      {
        type: 'value',
        name: '转化率(%)'
      }
    ],
    series: [
      {
        name: '访问量',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330, 1320]
      },
      {
        name: '转化率',
        type: 'line',
        yAxisIndex: 1,
        data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 75.6]
      }
    ]
  }
  lineChart.setOption(option)
}

// 初始化饼图
const initPieChart = () => {
  pieChart = echarts.init(pieChartRef.value, isDarkTheme.value ? 'dark' : 'light')
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '市场份额',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '产品A' },
          { value: 735, name: '产品B' },
          { value: 580, name: '产品C' },
          { value: 484, name: '产品D' },
          { value: 300, name: '产品E' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  pieChart.setOption(option)
}

// 初始化仪表盘
const initGaugeChart = () => {
  gaugeChart = echarts.init(gaugeChartRef.value, isDarkTheme.value ? 'dark' : 'light')
  const option = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: '性能指标',
        type: 'gauge',
        detail: {
          formatter: '{value}%'
        },
        data: [{ value: 75, name: '完成率' }]
      }
    ]
  }
  gaugeChart.setOption(option)
}

// 初始化所有图表
const initAllCharts = () => {
  nextTick(() => {
    initBarChart()
    initLineChart()
    initPieChart()
    initGaugeChart()
  })
}

// 刷新图表数据
const refreshCharts = () => {
  // 生成随机数据
  const randomData = () => Math.floor(Math.random() * 100) + 50

  // 更新柱状图
  if (barChart) {
    barChart.setOption({
      series: [
        {
          data: Array.from({ length: 6 }, randomData)
        },
        {
          data: Array.from({ length: 6 }, () => randomData() / 2)
        }
      ]
    })
  }

  // 更新折线图
  if (lineChart) {
    lineChart.setOption({
      series: [
        {
          data: Array.from({ length: 7 }, () => randomData() * 10)
        },
        {
          data: Array.from({ length: 7 }, () => randomData() / 10)
        }
      ]
    })
  }

  // 更新仪表盘
  if (gaugeChart) {
    gaugeChart.setOption({
      series: [
        {
          data: [{ value: randomData(), name: '完成率' }]
        }
      ]
    })
  }
}

// 切换主题
const toggleTheme = () => {
  isDarkTheme.value = !isDarkTheme.value
  // 重新初始化所有图表以应用新主题
  if (barChart) barChart.dispose()
  if (lineChart) lineChart.dispose()
  if (pieChart) pieChart.dispose()
  if (gaugeChart) gaugeChart.dispose()

  initAllCharts()
}

// 高亮选中的图表
const highlightChart = () => {
  // 移除所有高亮
  document.querySelectorAll('.chart-container').forEach(el => {
    el.classList.remove('highlighted')
  })

  // 高亮选中的图表
  if (selectedChart.value) {
    const chartMap = {
      bar: barChartRef,
      line: lineChartRef,
      pie: pieChartRef,
      gauge: gaugeChartRef
    }

    const targetRef = chartMap[selectedChart.value]
    if (targetRef.value) {
      targetRef.value.parentElement.classList.add('highlighted')
    }
  }
}

// 生命周期钩子
onMounted(() => {
  updateDeviceType()
  initAllCharts()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (barChart) barChart.dispose()
  if (lineChart) lineChart.dispose()
  if (pieChart) pieChart.dispose()
  if (gaugeChart) gaugeChart.dispose()
})
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0 0 15px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.resolution-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  font-size: 0.9em;
  opacity: 0.9;
}

.device-type {
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-weight: 500;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-container.highlighted {
  border-color: #667eea;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
}

.chart-container h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2em;
  font-weight: 600;
  text-align: center;
}

.chart {
  width: 100%;
  height: 300px;
}

.control-panel {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.control-panel h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3em;
  font-weight: 600;
}

.controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.control-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.control-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.chart-selector {
  padding: 10px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.chart-selector:focus {
  outline: none;
  border-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 15px;
  }

  .dashboard-header {
    padding: 20px;
  }

  .dashboard-header h1 {
    font-size: 2em;
  }

  .resolution-info {
    flex-direction: column;
    gap: 10px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart {
    height: 250px;
  }

  .controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-btn,
  .chart-selector {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 10px;
  }

  .dashboard-header h1 {
    font-size: 1.8em;
  }

  .chart {
    height: 200px;
  }

  .chart-container {
    padding: 10px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .dashboard {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .chart-container,
  .control-panel {
    background: #34495e;
    color: white;
  }

  .chart-container h3,
  .control-panel h3 {
    color: white;
  }
}
</style>
